<?php
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Plugin\Model\Quote;

use Magento\Quote\Api\Data\AddressInterface;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as ShipTableRatesCollectionFactory;

class AddressPlugin
{
    /**
     * @var ShipTableRatesCollectionFactory
     */
    protected $shipTableRatesCollectionFactory;

    /**
     * @param ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
     */
    public function __construct(
        ShipTableRatesCollectionFactory $shipTableRatesCollectionFactory
    ) {
        $this->shipTableRatesCollectionFactory = $shipTableRatesCollectionFactory;
    }

    /**
     * @param AddressInterface $subject
     * @param callable $proceed
     * @return mixed
     */
    public function aroundCollectShippingRates(AddressInterface $subject, callable $proceed)
    {
        $found = false;
        $price = null;
        $description = null;

        //get custom shipping rate set by admin
        foreach ($subject->getAllShippingRates() as $rate) {
            if ($rate->getCode() == $subject->getShippingMethod()) {
                $found = true;
                $price = $rate->getPrice();
                $description = $rate->getMethodTitle();
                break;
            }
        }

        $return = $proceed();

        if ($found) {
            //reset custom shipping rate
            foreach ($subject->getAllShippingRates() as $rate) {
                if ($rate->getCode() == $subject->getShippingMethod()) {
                    $rate->setPrice($price);
                    $rate->setCost($price);

                    if ($rate->getCarrier() === 'freeshipping') {
                        $this->ensureFreeShippingThresholdSubtitle($rate, $subject);
                    } else {
                        $rate->setMethodTitle($description);
                    }
                    break;
                }
            }
        }

        return $return;
    }

    /**
     * Ensure free shipping threshold subtitle is always present
     *
     * @param \Magento\Quote\Model\Quote\Address\RateResult\Method $rate
     * @param AddressInterface $address
     * @return void
     */
    protected function ensureFreeShippingThresholdSubtitle($rate, $address)
    {
        $currentTitle = $rate->getMethodTitle();

        if (strpos($currentTitle, 'For Orders Over') !== false) {
            return;
        }

        $quote = $address->getQuote();
        if (!$quote) {
            $rate->setMethodTitle('Free Shipping');
            return;
        }

        $country = $address->getCountryId();
        $subtotal = $quote->getBaseSubtotalWithDiscount();

        $threshold = $this->getFreeShippingThresholdForCountry($country, $subtotal);
        if ($threshold !== null) {
            $newTitle = sprintf('Free (For Orders Over $%.2f)', $threshold);
            $rate->setMethodTitle($newTitle);
        } else {
            $rate->setMethodTitle('Free Shipping');
        }
    }

    /**
     * Get free shipping threshold for the given country that is met
     *
     * @param string $country
     * @param float $subtotal
     * @return float|null
     */
    protected function getFreeShippingThresholdForCountry($country, $subtotal)
    {
        if (!$country || $subtotal <= 0) {
            return null;
        }

        $collection = $this->shipTableRatesCollectionFactory->create();
        $collection->addFieldToFilter('free_shipping', 1)
                   ->addFieldToFilter('min_order_amount', ['gt' => 0])
                   ->addFieldToFilter('min_order_amount', ['lteq' => $subtotal])
                   ->addFieldToFilter('countries', ['like' => "%{$country}%"])
                   ->setOrder('min_order_amount', 'ASC');

        $threshold = $collection->getFirstItem();

        return $threshold->getId() ? $threshold->getMinOrderAmount() : null;
    }
}
