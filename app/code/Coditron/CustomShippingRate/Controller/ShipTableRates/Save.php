<?php
/**
 * Webkul Software.
 *
 * @category  Webkul
 * @package   Webkul_MpSellerCategory
 * <AUTHOR> Software Private Limited
 * @copyright Webkul Software Private Limited (https://webkul.com)
 * @license   https://store.webkul.com/license.html
 */
namespace Coditron\CustomShippingRate\Controller\ShipTableRates;

class Save extends \Coditron\CustomShippingRate\Controller\AbstractShiprate
{
    /**
     * Seller Shipping Rate Save action
     * Handles both regular shipping methods and free shipping thresholds
     *
     * @return \Magento\Framework\Controller\Result\RedirectFactory
     */
    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        if ($this->getRequest()->isPost()) {
            try {
                if (!$this->_formKeyValidator->validate($this->getRequest())) {
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/manage',
                        ['_secure' => $this->getRequest()->isSecure()]
                    );
                }

                $postData = $this->getRequest()->getPostValue();
                $postData['seller_id'] = $this->getSellerId();
                $result = $this->_helper->validateData($postData);
                if ($result['error']) {
                    $this->messageManager->addError(__($result['msg']));

                    // Redirect back to the form page instead of manage page
                    $isThreshold = $this->getRequest()->getParam('is_threshold', false);
                    $rateId = $postData['id'] ?? null;

                    if ($rateId) {
                        // Editing existing record - redirect back to edit form
                        $params = ['shiptablerates_id' => $rateId, '_secure' => $this->getRequest()->isSecure()];
                        if ($isThreshold) {
                            $params['is_threshold'] = 1;
                        }
                        return $this->resultRedirectFactory->create()->setPath(
                            'coditron_customshippingrate/shiptablerates/edit',
                            $params
                        );
                    } else {
                        // Creating new record - redirect back to new form
                        $params = ['_secure' => $this->getRequest()->isSecure()];
                        if ($isThreshold) {
                            $params['is_threshold'] = 1;
                        }
                        return $this->resultRedirectFactory->create()->setPath(
                            'coditron_customshippingrate/shiptablerates/new',
                            $params
                        );
                    }
                }

                $sellerShiprate = $this->getSellerShiprate();


                if ($postData['id']) {
                    $sellerShiprate->addData($postData)->setShiptableratesId($postData['id']);
                } else {
                    $sellerShiprate->setData($postData);
                }

                $sellerShiprate->save();
                $id = $sellerShiprate->getShiptableratesId();

                $isThreshold = $this->getRequest()->getParam('is_threshold', false);
                $successMessage = $isThreshold ? "Free Shipping Threshold saved successfully." : "Shipping Rate saved successfully.";
                $this->messageManager->addSuccess(__($successMessage));
                $this->_helper->clearCache();

                $params = ['shiptablerates_id' => $id, '_secure' => $this->getRequest()->isSecure()];
                if ($isThreshold) {
                    $params['is_threshold'] = 1;
                }

                return $this->resultRedirectFactory->create()->setPath(
                    'coditron_customshippingrate/shiptablerates/edit',
                    $params
                );
            } catch (\Exception $e) {
                $this->messageManager->addError($e->getMessage());

                // Redirect back to the form page instead of manage page
                $postData = $this->getRequest()->getPostValue();
                $isThreshold = $this->getRequest()->getParam('is_threshold', false);
                $rateId = $postData['id'] ?? null;

                if ($rateId) {
                    // Editing existing record - redirect back to edit form
                    $params = ['shiptablerates_id' => $rateId, '_secure' => $this->getRequest()->isSecure()];
                    if ($isThreshold) {
                        $params['is_threshold'] = 1;
                    }
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/edit',
                        $params
                    );
                } else {
                    // Creating new record - redirect back to new form
                    $params = ['_secure' => $this->getRequest()->isSecure()];
                    if ($isThreshold) {
                        $params['is_threshold'] = 1;
                    }
                    return $this->resultRedirectFactory->create()->setPath(
                        'coditron_customshippingrate/shiptablerates/new',
                        $params
                    );
                }
            }
        } else {
            return $this->resultRedirectFactory->create()->setPath(
                'coditron_customshippingrate/shiptablerates/manage',
                ['_secure' => $this->getRequest()->isSecure()]
            );
        }
    }
}
